import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { ServiceBase } from '../base/serviceBase';
import { ClientAttachedFilesDto } from './clientAttachedFilesDto';

export class ClientAttachedFilesService extends ServiceBase {
    constructor() {
        super(Endpoint.ClientAttachedFiles);
    }

    public async sendAgreements(payload: string, clientId: number) {
        let result = await httpApi.post(this.endpoint.Custom(`SendAgreements`), {payload, clientId});
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async createNew(createClientAttachedFileInput: ClientAttachedFilesDto) {
        let result = await httpApi.post(this.endpoint.Create(), createClientAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async update(updateClientAttachedFileInput: ClientAttachedFilesDto) {
        let result = await httpApi.put(this.endpoint.Update(), updateClientAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async checkIsClientHaveAgreements(clientId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`CheckIsClientHaveAgreements/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async sendPolicyMail(policyId: number) {
        let result = await httpApi.post(this.endpoint.Custom(`SendPolicyMail/?policyId=${policyId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportClientAttachedFilesService: ClientAttachedFilesService = new ClientAttachedFilesService();
export default exportClientAttachedFilesService;