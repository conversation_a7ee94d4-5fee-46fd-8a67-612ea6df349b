import { Pi<PERSON>t, De<PERSON>ultButton, PrimaryButton, mergeStyleSets, LayerHost, PivotItem, MessageBarType, Dialog, DialogFooter, DialogType, Spinner, SpinnerSize } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { KeysToIdMapper, ProductDto } from '../../../services/product/productDto';
import { additionalTheme, myTheme } from '../../../styles/theme';
import { GenericContentView, IGenericContentViewProps } from '../../BaseComponents/genericContentView';
import { defaultKeysToIdMapper, defaultProduct } from '../../../stores/productStore';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { PolicyFormStepsLayer } from './policyFormStepsLayer';
import { getGrandNodeLanguage } from '../../../utils/languageUtils';
import { defaultClient } from '../../../stores/clientStore';
import { buildRequestBody, getDataFromCalculationToEdit, getDataFromOrderToEdit, getUserFieldsFromProductAttributeMapping, getValueBasedOnClonedValue, mapAttributeKeyToId } from '../../../utils/policyCalculationUtils';
import { catchErrorMessage, filterBySome, isJsonString, peselDecode } from '../../../utils/utils';
import AppConfig from '../../../lib/appconfig';
import { OrderDto } from '../../../services/order/dto/orderDto';
import { AutoCalculationOwnerType } from '../../../services/policyCalculation/autoCalculationOwnerEnums';
import { ClientDto } from '../../../services/client/dto/clientDto';
import { CalculationSectionType } from '../../../services/dto/calculationSectionTypeEnums';
import productService from '../../../services/product/productService';
import { CalculationDto } from '../../../services/calculation/dto/calculationDto';
import { PolicyType } from '../../../services/policy/policyTypeEnums';
import { hourInMs, saveInStorage, validateLocalStorageKeyAndTimestamp } from '../../../utils/localStorageUtils';
import { PolicyFormStep2 } from './policyFormStep2';
import { PolicyFormStep1 } from './policyFormStep1';
import insurancePolicyService from '../../../services/insurancePolicy/insurancePolicyService';
import { PolicyFormStep3 } from './policyFormStep3';
import { ApkAttachedFilesDto } from '../../../services/apkAttachedFiles/apkAttachedFilesDto';
import vehicleConfigService from '../../../services/vehicleConfig/vehicleConfigService';
import { IContentViewState } from '../../BaseComponents/IContentViewState';

const classNames = mergeStyleSets({
    pageTitle: {
        color: additionalTheme.grey,
        fontSize: '26px',
        margin: "35px 0 65px 0"
    },
    fontBold: {
        fontWeight: '800',
    },
    layerHost: {
        marginBottom: '20px',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    confrimButtonWrapper: {
        display: 'inline-flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'center',
        width: 'fit-content',
    },
    loadSpinner: {
        display: 'inline-flex',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

@inject(Stores.LanguageStore)
@inject(Stores.CountryStore)
@inject(Stores.SportDisciplineStore)
@inject(Stores.SportInsuranceCoverageStore)
@inject(Stores.ClientStore)
@inject(Stores.VehicleStore)
@inject(Stores.ProductStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.OrderStore)
@inject(Stores.CalculationStore)
@inject(Stores.InsurerStore)
@inject(Stores.ApkAttachedFilesStore)
export class PolicyFormContentView extends GenericContentView {
    private allProducts: ProductDto[] = [];
    private product: ProductDto | undefined = defaultProduct;
    private selectedProduct: string | number | undefined;
    private client: ClientDto | undefined = defaultClient;
    private tempSelectedClient: string = "";
    private tempSelectedVehicle: string = "";
    private tempSelectedApk: ApkAttachedFilesDto | undefined = undefined;
    private selectedCustomer: string = "";
    private selectedClient: string = "";
    private selectedClientData: ClientDto | undefined = defaultClient;
    private inputsTypeValuePairs: any = {};
    private adjustInputsChangedManually: any = {};
    private inputsChangedManually: any = {};
    private inputsIdUserFieldsPairs: any = {};
    private tempInputsTypeValuePairsFromEdit: any = {};
    private clientTypeValuePairs: any = {};
    private gnLanguage: any = {};
    private step: number = 1;
    private prevProductId: string | undefined = "";
    private clientDataFilled: boolean = false;
    private showConfirmationDialog: boolean = false;
    private dialogConfirmed: boolean = false;
    private summaryMessageBoxData: any = {
        address: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        createOrder: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyApplication: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        sendSms: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyCalculation: {
            type: MessageBarType.error,
            text: "",
            hide: true
        },
        policyFinalization: {
            type: MessageBarType.success,
            text: L("Success"),
            hide: false
        }
    };
    private customInputsData: any = {};
    private editedOrder: any = {};
    private isEditMode: boolean = false;
    private isFastCalculation: boolean = false;
    private _isMounted: boolean = false;
    private templateInputsForCalculationAdjust: any = {};
    private getCalculationsRawResponse: any = {};
    private autoCalculationOwner: string = "";
    private inputErrors: number = 0;
    private blockNextStepButton: boolean = false;
    private savedProductAttributeMappings: any = {
        productAttributeMappingsToWatch: {} as any,
        productAttributeMappingsToChange: {} as any,
    };
    private _returnedCreatedOrder: any;
    private _returnedAbpPolicyId: number = 0;
    private keysToIdMapper: KeysToIdMapper = defaultKeysToIdMapper;
    private allUserFields: any[] = [];
    private inputValuePairsStringified: string = '';
    private savedMappedIdsForLaterUse: any = {};
    private initDataCatchedForCustomInputs: any = {
        insurerIsTravelParticipant: false as boolean,
    };
    private productAttributes: any = {};
    private insurers: any = {};
    private setDelayedInputNewValue: any = {};
    private prevTravelTypeOfCalculation: string = '';
    private manualPolicyCreateRequestSent: boolean = false;
    private eurotaxInfoexpertFormData: any = {
        type: '',
        brand: '',
        year: '',
        fuelType: '',
        engineCapacity: '',
        model: '',
        enginePower: '',
        eurotax: '',
        infoExpert: '',
        vin: '',
    };
    private childTabSwitch = null;
    private formChildTabSwitch = (func: any) => {
        this.childTabSwitch = func;
    };
    private tabsSelectedKey: any;
    private tabs: any[] = [
        // { key: UserField.Key, name: displayName, items: [{value, order}, {value, order}] }
    ];
    private savedTemplateInputsForTable: any = {};
    private gotUserFieldsForSelectedProduct: string = '';

    async componentDidMount() {
        this._isMounted = true;

        this.toggleAsyncActionFlag(true);
        
        if(validateLocalStorageKeyAndTimestamp('policyCalculationKeysToIdMapper', hourInMs * 12)) {
            this.keysToIdMapper = JSON.parse(localStorage.getItem('policyCalculationKeysToIdMapper')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading product keys for ID mapper.'));
            this.keysToIdMapper = await productService.getProductKeysToIdMapper();
            saveInStorage('policyCalculationKeysToIdMapper', {data: this.keysToIdMapper, timestamp: new Date().getTime()});
        }

        this.savedMappedIdsForLaterUse = {
            insurerIsTravelParticipant: this.mapKeyToId("mapAttributeNameToId", "insurerIsTravelParticipant"),
            travelCountryList: this.mapKeyToId("mapAttributeNameToId", "travelCountryList"),
            insurerIsTravelParticipantOption: this.mapKeyToId("mapAttributeValueToOptionId", "insurerIsTravelParticipant"),
            travelSportList: this.mapKeyToId("mapAttributeNameToId", "travelSportList"),
            travelTypeOfCalculation: this.mapKeyToId("mapAttributeNameToId", "travelTypeOfCalculation"),
            travelTypeOfCalculationLimitedOption: this.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_LIMITED"),
            travelTypeOfCalculationFullOption: this.mapKeyToId("mapAttributeValueToOptionId", "TYPE_OF_CALCULATION_FULL"),
            autoProductionYear: this.mapKeyToId("mapAttributeNameToId", "productionYear"),
            autoFirstRegistrationDate: this.mapKeyToId("mapAttributeNameToId", "firstRegistrationDate"),
            autoVehicleAverageAnnualMileage: this.mapKeyToId("mapAttributeNameToId", "vehicleAverageAnnualMileage"),
            autoVehicleParkingPlace: this.mapKeyToId("mapAttributeNameToId", "vehicleParkingPlace"),
            autoMaritalStatus: this.mapKeyToId("mapAttributeNameToId", "maritalStatus"),
            autoVehicleChildrenUnder26: this.mapKeyToId("mapAttributeNameToId", "vehicleChildrenUnder26"),
            autoYearOfPurchaseOfTheVehicle: this.mapKeyToId("mapAttributeNameToId", "yearOfPurchaseOfTheVehicle"),
            autoDrivingLicenceIssueYear: this.mapKeyToId("mapAttributeNameToId", "drivingLicenceIssueYear"),
            autoVin: this.mapKeyToId("mapAttributeNameToId", "vin"),
            autoMileage: this.mapKeyToId("mapAttributeNameToId", "mileage"),
        };

        if(validateLocalStorageKeyAndTimestamp('policyCalculationGnLanguage', hourInMs * 12)) {
            this.gnLanguage = JSON.parse(localStorage.getItem('policyCalculationGnLanguage')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading languages.'));
            await this.props.languageStore?.getAll(this.props.languageStore?.defaultRequest);
            this.gnLanguage = getGrandNodeLanguage(this.props.languageStore?.dataSet);
            saveInStorage('policyCalculationGnLanguage', {data: this.gnLanguage, timestamp: new Date().getTime()});
        }

        if(validateLocalStorageKeyAndTimestamp('policyCalculationAllProducts', hourInMs * 12)) {
            this.allProducts = JSON.parse(localStorage.getItem('policyCalculationAllProducts')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading products.'));
            await productService.getAllForDropdown().then((result: any) => {
                if(!!result && Array.isArray(result)) {
                    this.allProducts = result;
                    saveInStorage('policyCalculationAllProducts', {data: result, timestamp: new Date().getTime()});
                }
            });
        }

        if(!this.props.countryStore?.dataSet || (this.props.countryStore.dataSet && this.props.countryStore.dataSet.totalCount <= 0)) {
            this.changeLoadSpinnerLabel(L('Downloading countries.'));
            await this.props.countryStore!.getAll(this.props.countryStore!.defaultRequest);
        }
        
        if(this.props.policyCalculation?.payloadType === "customer" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading client data.'));
            this.client = await this.props.clientStore?.get({ id: this.props.policyCalculation?.payloadId } as ClientDto);
            this.selectedCustomer = this.props.clientStore?.model.customerId ? this.props.clientStore?.model?.customerId : "";
            this.selectedClient = this.props.policyCalculation?.payloadId;
            this.tempSelectedClient = this.props.policyCalculation?.payloadId;
            this.selectedClientData = this.client;
            this.clientDataFilled = true;
        }
        
        if(this.props.policyCalculation?.payloadType === "product" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.selectedProduct = this.props.policyCalculation?.payloadId;
        }

        if(validateLocalStorageKeyAndTimestamp('insurersData', hourInMs * 12)) {
            this.insurers = JSON.parse(localStorage.getItem('insurersData')!).data;
        } else {
            if(!this.props.insurerStore || !this.props.insurerStore.dataSet || this.props.insurerStore.dataSet.totalCount === 0) {
                this.changeLoadSpinnerLabel(L('Downloading insurers data.'));
                await this.props.insurerStore?.getAll(this.props.insurerStore?.defaultRequest);
                this.insurers = this.props.insurerStore && this.props.insurerStore.dataSet && this.props.insurerStore.dataSet.items ? this.props.insurerStore.dataSet.items : {};
                saveInStorage('insurersData', {data: this.insurers, timestamp: new Date().getTime()});
            }
        }

        if(this.props.policyCalculation?.payloadType === "order" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading order data.'));
            await this.props.orderStore?.get({ id: this.props.policyCalculation?.payloadId } as OrderDto);
            this.editedOrder = this.props.orderStore?.model;
            this.isEditMode = true;

            this.selectedProduct = this.editedOrder.OrderItems[0].Id;
            let productId = this.selectedProduct?.toString() ?? "";
            let dataFromOrder = getDataFromOrderToEdit(this.editedOrder.OrderItems[0].Attributes, productId, this.keysToIdMapper);
            
            this.selectedCustomer = this.editedOrder.CustomerId;

            if(dataFromOrder && dataFromOrder.dataObjFiltered) {
                dataFromOrder.dataObjFiltered.forEach((dataObj: any) => {
                    if(!!dataObj.valueId) {
                        this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.valueId;
                    } else {
                        this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.value;
                    }
                });
            } else if(dataFromOrder && dataFromOrder.apiCall && dataFromOrder.apiCall.source) {
                for(let key in dataFromOrder.apiCall.source) {
                    if(dataFromOrder.apiCall.source.hasOwnProperty(key)) {
                        this.tempInputsTypeValuePairsFromEdit[key] = dataFromOrder.apiCall.source[key];
                    }
                };
            }
        }

        if(this.props.policyCalculation?.payloadType === "calculation" && this.props.policyCalculation?.payloadId && this.props.policyCalculation?.payloadId?.length > 0) {
            this.changeLoadSpinnerLabel(L('Downloading calculation data.'));
            await this.props.calculationStore?.get({ id: this.props.policyCalculation?.payloadId } as CalculationDto).catch((error: any) => {
                this.catchError(error, "other");
            });
            const editedCalculation = this.props.calculationStore?.model;
            this.isEditMode = true;

            if(!!editedCalculation) {
                let dataFromCalculation = getDataFromCalculationToEdit(editedCalculation?.payload ? editedCalculation?.payload : '');

                if(dataFromCalculation && Object.keys(dataFromCalculation).length > 0) {
                    if(dataFromCalculation.type === PolicyType.Vehicle) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta");
                    } else if(dataFromCalculation.type === PolicyType.Home) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-domu");
                    } else if(dataFromCalculation.type === PolicyType.Life) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-na-życie");
                    } else if(dataFromCalculation.type === PolicyType.Travel) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "podróż");
                    } else if(dataFromCalculation.type === PolicyType.Children) {
                        this.selectedProduct = this.mapKeyToId("mapProductNameToProductId", "nnw-dziecka");
                    }
                    this.selectedCustomer = editedCalculation.clientId.toString();
                    this.selectedClient = editedCalculation.clientId.toString();

                    let euroTaxAndInfoExpertSetIterator: number = 0;
        
                    if(dataFromCalculation && dataFromCalculation.data) {
                        let concatedDataFromCalculation: any[] = [...dataFromCalculation.data];

                        if(dataFromCalculation.additionalData) {
                            let parsedAdditionalData = isJsonString(dataFromCalculation.additionalData) ? JSON.parse(dataFromCalculation.additionalData) : [];
                            if(parsedAdditionalData && parsedAdditionalData.length > 0) {
                                concatedDataFromCalculation = [...dataFromCalculation.data, ...parsedAdditionalData];
                            }
                        }

                        concatedDataFromCalculation.forEach((dataObj: any) => {
                            if(!!dataObj.valueId) {
                                this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = isJsonString(dataObj.valueId) ? JSON.parse(dataObj.valueId) : dataObj.valueId;
                            } else {
                                this.tempInputsTypeValuePairsFromEdit[dataObj.productAttributeId] = dataObj.value;
                            }

                            if(!dataObj.is_for_apk) {
                                if(this.inputsIdUserFieldsPairs[dataObj.productAttributeId]) {
                                    this.inputsIdUserFieldsPairs[dataObj.productAttributeId] = [...this.inputsIdUserFieldsPairs[dataObj.productAttributeId], {"Key": "core_path", "Value": dataObj.core_path}, {"Key": "core_type", "Value": dataObj.core_type}];
                                } else {
                                    this.inputsIdUserFieldsPairs[dataObj.productAttributeId] = [{"Key": "core_path", "Value": dataObj.core_path}, {"Key": "core_type", "Value": dataObj.core_type}];
                                }
                            }

                            if(dataObj.core_path === 'VehicleInfo.EurotaxCarId') {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L('Success! Eurotax ID is now set.');
                                euroTaxAndInfoExpertSetIterator++;
                            } else if(dataObj.core_path === 'VehicleInfo.InfoExpertId') {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L("Success! Expert's information ID is now set.");
                                euroTaxAndInfoExpertSetIterator++;
                            }
    
                            if(euroTaxAndInfoExpertSetIterator === 2) {
                                this.tempInputsTypeValuePairsFromEdit[this.mapKeyToId("mapAttributeNameToId", "findAVehicle")] = L("Eurotax ID and Expert's information ID is now set.");
                            }
                        });
                    }
                }
            }
        }

        let isThisOriginProduct: boolean = (this.prevProductId && this.prevProductId?.length > 0 ? false : true);

        if(!!this.selectedProduct) {
            if(validateLocalStorageKeyAndTimestamp(`policyCalculationProduct${this.selectedProduct}`, hourInMs * 12, true)) {
                this.product = JSON.parse(sessionStorage.getItem(`policyCalculationProduct${this.selectedProduct}`)!).data;
                this.clearDataOnProductChange();
    
                if(isThisOriginProduct && this.isEditMode) {
                    this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                }
            } else {
                this.changeLoadSpinnerLabel(L('Downloading product data.'));
                await this.props.productStore?.getProductWithMappingsForProductId(this.selectedProduct.toString()).then((productResult: any) => {
                    this.product = productResult;
                    this.clearDataOnProductChange();
    
                    if(isThisOriginProduct && this.isEditMode) {
                        this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                    }
                    saveInStorage(`policyCalculationProduct${this.selectedProduct}`, {data: this.product, timestamp: new Date().getTime()}, true);
                });
            }
        }

        if(validateLocalStorageKeyAndTimestamp('policyCalculationProductAttributes', hourInMs * 12)) {
            this.productAttributes = JSON.parse(localStorage.getItem('policyCalculationProductAttributes')!).data;
        } else {
            this.changeLoadSpinnerLabel(L('Downloading product attributes.'));
            await this.props.productAttributeStore?.getAll(this.props.productAttributeStore!.defaultRequest);
            this.productAttributes = this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.items ? this.props.productAttributeStore.dataSet.items : {};
            saveInStorage('policyCalculationProductAttributes', {data: this.productAttributes, timestamp: new Date().getTime()});
        }
        
        this.isDataLoaded = true;
        this.toggleAsyncActionFlag(false);

        this.loadSpinnerCustomLabel = null;
        this.controlledForceUpdate('line 203');
    }

    async componentDidUpdate(prevProps: Readonly<IGenericContentViewProps>, prevState: Readonly<IContentViewState>, snapshot?: any): void {
        if(!!this.selectedProduct && this.selectedProduct !== this.gotUserFieldsForSelectedProduct) {
            this.gotUserFieldsForSelectedProduct = this.selectedProduct.toString();

            if(validateLocalStorageKeyAndTimestamp(`userFieldsForProductId${this.selectedProduct}`, hourInMs * 12, true)) {
                this.allUserFields = JSON.parse(sessionStorage.getItem(`userFieldsForProductId${this.selectedProduct}`)!).data;
                this.forceUpdate();
            } else {
                this.toggleAsyncActionFlag(true);
                this.changeLoadSpinnerLabel(L('Downloading user fields for selected product.'));

                await productService.getUserFieldsForProductId(this.selectedProduct.toString()).then((result: any) => {
                    if(!!result && Array.isArray(result)) {
                        this.allUserFields = result;
                        saveInStorage(`userFieldsForProductId${this.selectedProduct}`, {data: result, timestamp: new Date().getTime()}, true);
                    }
                });

                this.changeLoadSpinnerLabel(null);
                this.toggleAsyncActionFlag(false);
            }
        }
    }

    componentWillUnmount() {
        this._isMounted = false;

        this.inputsTypeValuePairs = {};
        this.inputsIdUserFieldsPairs = {};
        this.customInputsData = {};
        this.selectedCustomer = "";
        this.selectedClient = "";
        this.selectedProduct = undefined;
        this.prevProductId = undefined;
    }

    mapKeyToId(mapType: string, key: string): string {
        switch(mapType) {
            case "mapAttributeNameToId":
                return this.mapAttributeNameToId(key);
            case "mapAttributeValueToOptionId":
                return this.mapAttributeValueToOptionId(key);
            case "mapProductNameToProductId":
                return this.mapProductNameToProductId(key);
            case "mapProductIdToType":
                return this.mapProductIdToType(key);
            default:
                return "";    
        }
    }

    private mapAttributeNameToId(name: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.attributeNameToIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);

            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private mapAttributeValueToOptionId(name: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper){
            let findedId = this.keysToIdMapper.attributeValueToOptionIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);
            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private mapProductNameToProductId(name: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productKeyToProductIdList
                                                    .filter(x => x.key === name)
                                                    .map(x => x.id);
            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private mapProductIdToType(id: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productIdToTypeList
                                                    .filter(x => x.id === id)
                                                    .map(x => x.key);
            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private getProductTypeByProduct(product: ProductDto | undefined) {
        return this.mapProductIdToType(this.getProductId(product));
    }

    getMapNameByProduct(product: ProductDto | undefined) {
        return this.mapProductIdToMapName(this.getProductId(product));
    }

    private mapProductIdToMapName(id: string) {
        if (this.keysToIdMapper !== defaultKeysToIdMapper) {
            let findedId = this.keysToIdMapper.productIdToMapNameList
                                                    .filter(x => x.id === id)
                                                    .map(x => x.key);
            if(findedId[0] !== undefined) {
                return findedId[0];
            }
        }
        return "";
    }

    private getProductId(product: ProductDto | undefined) {
        if (product !== null && product !== undefined) {
            return product?.Id;
        }
        return "";
    }

    private controlledForceUpdate(whereCalled?: string) {
        if(this._isMounted === true) {
            this.forceUpdate();
        }
    }

    private changeLoadSpinnerLabel(newValue: string | null) {
        this.loadSpinnerCustomLabel = newValue;
        this.controlledForceUpdate('line 535');
    }

    private clearDataOnProductChange() {
        if(this.prevProductId !== this.product?.Id) {
            this.inputsTypeValuePairs = {};
            this.customInputsData = {};
            this.prevProductId = this.product?.Id;
        }
    }

    private loopInputsTypeValuePairs() {
        for(let key in this.inputsTypeValuePairs) {
            if(this.inputsTypeValuePairs.hasOwnProperty(key)) {
                this.catchDataForCustomInputs(key, this.inputsTypeValuePairs[key]);
            }
        }

        this.controlledForceUpdate('line 241');
    }

    private catchDataForCustomerInputs(id: string, value: string, userFields: any) {
        if(!!userFields) {
            userFields.some((UserField: any) => {
                if(UserField.Key === "key") {
                    let splittedKey = UserField.Value.split('.');
                    
                    if(['Insurer', 'VehicleOwner', 'VehicleCoOwner', 'VehicleUser'].includes(splittedKey[1])) {
                        let newSectionKey: string = "";
                        switch(splittedKey[1]) {
                            case 'Insurer':
                                newSectionKey = CalculationSectionType.Insurer;
                                break;
                            case 'VehicleOwner':
                                newSectionKey = CalculationSectionType.Owner;
                                break;
                            case 'VehicleCoOwner':
                                newSectionKey = CalculationSectionType.CoOwner;
                                break;
                            case 'VehicleUser':
                                newSectionKey = CalculationSectionType.User;
                                break;
                        }
    
                        if(!this.clientTypeValuePairs[newSectionKey]) {
                            this.clientTypeValuePairs[newSectionKey] = {};
                        }
                        
                        this.clientTypeValuePairs[newSectionKey][UserField.Value] = value;
                    }
    
                    return true;
                }
    
                return false;
            });
        }
    }

    private saveInputsForCalculationAdjust(inputs: any) {
        this.templateInputsForCalculationAdjust = inputs;

        for(let key in inputs) {
            if(inputs.hasOwnProperty(key)) {
                if(inputs[key].attr && inputs[key].attr.Id && inputs[key].userFields) {
                    this.inputsIdUserFieldsPairs[inputs[key].attr.Id] = inputs[key].userFields;
                }
            }
        }

        this.controlledForceUpdate('line 291');
    }

    private catchDataForCustomInputs(id: string, value: string) {
        let caseFoundFlag: boolean = false;

        switch(id) {
            case 'vehicleBrand':
                this.customInputsData['vehicleBrand'] = value;
                delete this.customInputsData['vehicleModel'];
                caseFoundFlag = true;
            break;
                
            case 'vehicleModel':
                this.customInputsData['vehicleModel'] = value;
                caseFoundFlag = true;
            break;
    
            // eurotax
            case 'vehicleTypeId':
                this.customInputsData['vehicleTypeId'] = value;
                delete this.customInputsData['vehicleBrandId'];
                delete this.customInputsData['productionYear'];
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                caseFoundFlag = true;
            break;
    
            case 'vehicleBrandId':
                if(!!this.customInputsData['vehicleBrandId']) {
                    delete this.customInputsData['productionYear'];
                }
    
                this.customInputsData['vehicleBrandId'] = value;
                delete this.customInputsData['productionYear'];
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                caseFoundFlag = true;
            break;
    
            case 'productionYear':
                this.customInputsData['productionYear'] = value;
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear] = typeof value === 'string' && !!value ? parseInt(value) : value;
                delete this.customInputsData['fuelType'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                delete this.customInputsData['engineCapacity'];
                
                caseFoundFlag = true;
            break;

            case 'fuelType':
                this.customInputsData['fuelType'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['vehicleModelId'];
                delete this.customInputsData['engineCapacity'];
                delete this.customInputsData['enginePower'];
                caseFoundFlag = true;
            break;

            case 'engineCapacity':
                this.customInputsData['engineCapacity'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                caseFoundFlag = true;
            break;
                
            case 'vehicleModelId':
                this.customInputsData['vehicleModelId'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                delete this.customInputsData['enginePower'];
                
                caseFoundFlag = true;
            break;
                
            case 'enginePower':
                this.customInputsData['enginePower'] = value;
                delete this.customInputsData['vehicleConfigurationId'];
                delete this.customInputsData['vehicleConfigurationEurotaxId'];
                delete this.customInputsData['vehicleConfigurationInfoExpertId'];
                caseFoundFlag = true;
            break;
                
            case 'vehicleConfigurationId':
                this.customInputsData['vehicleConfigurationId'] = value;
                caseFoundFlag = true;
            break;
            
            case 'vehicleConfigurationEurotaxId':
                this.customInputsData['vehicleConfigurationEurotaxId'] = value;
                caseFoundFlag = true;
            break;

            case 'vehicleConfigurationInfoExpertId':
                this.customInputsData['vehicleConfigurationInfoExpertId'] = value;
                caseFoundFlag = true;
            break;

            case this.savedMappedIdsForLaterUse.insurerIsTravelParticipant:
            case this.savedMappedIdsForLaterUse.travelTypeOfCalculation:
                let valueIsFromCalculationTypeInput: boolean = value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationLimitedOption || value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption ? true : false;
                let isCalculationTypeFullData: boolean = false;
                if(value === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption || 
                    (!valueIsFromCalculationTypeInput && this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.travelTypeOfCalculation] === this.savedMappedIdsForLaterUse.travelTypeOfCalculationFullOption)
                ) {
                    isCalculationTypeFullData = true;
                }
                
                const travelerNumberId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerNumber") : this.mapKeyToId("mapAttributeNameToId", "travelerNumberFullData");
                const travelerFirstNameId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerFirstName") : this.mapKeyToId("mapAttributeNameToId", "travelerFirstNameFullData");
                const travelerSurnameId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerSurname") : this.mapKeyToId("mapAttributeNameToId", "travelerSurnameFullData"); 
                const travelerDateOfBirthId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "travelerDateOfBirth") : this.mapKeyToId("mapAttributeNameToId", "travelerDateOfBirthFullData");
                const travelerCityFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCityFullData") : '';
                const travelerHouseNumberFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerHouseNumberFullData") : '';
                const travelerPeselFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPeselFullData") : '';
                const travelerCountyFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCountyFullData") : '';
                const travelerCountryFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerCountryFullData") : '';
                const travelerStreetFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerStreetFullData") : '';
                const travelerPostCodeFullDataId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPostCodeFullData") : '';
                const travelerPhoneNumberId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelerPhoneNumberFullData") : '';

                const numberOfPeopleTravelingId: string = isCalculationTypeFullData === false ? this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");
                const travelersTableId: string = this.mapKeyToId("mapAttributeNameToId", "travelersTable");
                const travelersTableFullDataId: string = this.mapKeyToId("mapAttributeNameToId", "travelersTableFullData");

                const travelInsurerFirstNameId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerFirstName");
                const travelInsurerSurnameId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerSurname");
                const travelInsurerPeselId: string = this.mapKeyToId("mapAttributeNameToId", "travelInsurerPesel");
                const travelInsurerPostCodeId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerPostCode") : '';
                const travelInsurerCityId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCity") : '';
                const travelInsurerHouseNumberId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerHouseNumber") : '';
                const travelInsurerMobilePhoneId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerMobilePhone") : '';
                const travelInsurerStreetId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerStreet") : '';
                const travelInsurerCountyId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCounty") : '';
                const travelInsurerCountryId: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "travelInsurerCountry") : '';

                let decodePesel: any = peselDecode(this.inputsTypeValuePairs[travelInsurerPeselId]);

                const travelersTableIdToChange: string = isCalculationTypeFullData === false ? travelersTableId : travelersTableFullDataId;
                const travelersTableIdToRemove: string = isCalculationTypeFullData === true ? travelersTableId : travelersTableFullDataId;
                const travelersNumberOfPeopleTravelingToReset: string = isCalculationTypeFullData === true ? this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTraveling") : this.mapKeyToId("mapAttributeNameToId", "numberOfPeopleTravelingFullData");

                if((value === this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption || 
                    (valueIsFromCalculationTypeInput && this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] === this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption)) &&
                    (!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] || 
                        (this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.insurerIsTravelParticipant] !== this.savedMappedIdsForLaterUse.insurerIsTravelParticipantOption ||
                        (valueIsFromCalculationTypeInput && value !== this.prevTravelTypeOfCalculation)) ||
                        this.initDataCatchedForCustomInputs.insurerIsTravelParticipant === false)
                ) {
                    let addNewRow: boolean = false;

                    if(valueIsFromCalculationTypeInput) {
                        this.prevTravelTypeOfCalculation = value;
                    }

                    if(isJsonString(this.inputsTypeValuePairs[travelersTableIdToChange])) {
                        const parsedTableData: any = JSON.parse(this.inputsTypeValuePairs[travelersTableIdToChange]);
                        let newObjToPush: any = undefined;

                        if(isCalculationTypeFullData) {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                                [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                                [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                                [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                                [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                                [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                                [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                                [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            };
                        } else {
                            newObjToPush = {
                                // [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerDateOfBirthId]: decodePesel && decodePesel.date ? decodePesel.date : '',
                            };
                        }

                        if(!this.inputsTypeValuePairs[travelersTableIdToChange].includes(JSON.stringify(newObjToPush).substring(1).slice(0, -15))) {
                            newObjToPush[travelerNumberId] = this.inputsTypeValuePairs[numberOfPeopleTravelingId];
                            parsedTableData[Object.keys(parsedTableData).length] = newObjToPush;
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify(parsedTableData);
                            addNewRow = true;
                        }
                    } else {
                        addNewRow = true;

                        if(isCalculationTypeFullData) {
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify({0: {
                                [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerPeselFullDataId]: this.inputsTypeValuePairs[travelInsurerPeselId],
                                [travelerCityFullDataId]: this.inputsTypeValuePairs[travelInsurerCityId],
                                [travelerStreetFullDataId]: this.inputsTypeValuePairs[travelInsurerStreetId],
                                [travelerHouseNumberFullDataId]: this.inputsTypeValuePairs[travelInsurerHouseNumberId],
                                [travelerPostCodeFullDataId]: this.inputsTypeValuePairs[travelInsurerPostCodeId],
                                [travelerCountyFullDataId]: this.inputsTypeValuePairs[travelInsurerCountyId],
                                [travelerCountryFullDataId]: this.inputsTypeValuePairs[travelInsurerCountryId],
                                [travelerPhoneNumberId]: this.inputsTypeValuePairs[travelInsurerMobilePhoneId],
                            }});
                        } else {
                            this.inputsTypeValuePairs[travelersTableIdToChange] = JSON.stringify({0: {
                                [travelerNumberId]: this.inputsTypeValuePairs[numberOfPeopleTravelingId],
                                [travelerFirstNameId]: this.inputsTypeValuePairs[travelInsurerFirstNameId],
                                [travelerSurnameId]: this.inputsTypeValuePairs[travelInsurerSurnameId],
                                [travelerDateOfBirthId]: decodePesel && decodePesel.date ? decodePesel.date : '',
                            }});
                        }
                    }

                    if(addNewRow === true) {
                        if(!this.inputsTypeValuePairs[numberOfPeopleTravelingId] || this.inputsTypeValuePairs[numberOfPeopleTravelingId].length === 0 || 
                            (typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'string' && this.inputsTypeValuePairs[numberOfPeopleTravelingId] === '0') ||
                            (typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'number' && this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 0)
                        ) {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = '1';
                        } else if(typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'string') {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = (parseInt(this.inputsTypeValuePairs[numberOfPeopleTravelingId]) + 1).toString();
                        } else if(typeof this.inputsTypeValuePairs[numberOfPeopleTravelingId] === 'number') {
                            this.inputsTypeValuePairs[numberOfPeopleTravelingId] = (this.inputsTypeValuePairs[numberOfPeopleTravelingId] + 1).toString();
                        }
                    }
                    
                    this.initDataCatchedForCustomInputs = {...this.initDataCatchedForCustomInputs, insurerIsTravelParticipant: true};
                }
                delete this.inputsTypeValuePairs[travelersTableIdToRemove];
                this.inputsTypeValuePairs[travelersNumberOfPeopleTravelingToReset] = '0';
            break;

            case this.savedMappedIdsForLaterUse.travelCountryList:
                const geographicalZoneId: string = this.mapKeyToId("mapAttributeNameToId", "geographicalZone");

                let foundCodes: string[] = [];
                if(!!value && isJsonString(value)) {
                    const parsedValue: any = JSON.parse(value);
                    parsedValue.forEach((element: any) => {
                        if(element.code && !!element.code) {
                            const getFirst3Chars: string = element.code.substring(0,3);
                            if(!foundCodes.includes(getFirst3Chars)) {
                                foundCodes.push(getFirst3Chars);
                            }
                        }
                    });
                }

                if(foundCodes.includes('whw')) {
                    this.inputsTypeValuePairs[geographicalZoneId] = this.mapKeyToId("mapAttributeValueToOptionId", "GEOGRAPHICAL_ZONE_WORLD");
                } else if(foundCodes.includes('wwu')) {
                    this.inputsTypeValuePairs[geographicalZoneId] = this.mapKeyToId("mapAttributeValueToOptionId", "GEOGRAPHICAL_ZONE_WORLD_WITHOUT_USA");
                } else if(foundCodes.includes('eur')) {
                    this.inputsTypeValuePairs[geographicalZoneId] = this.mapKeyToId("mapAttributeValueToOptionId", "GEOGRAPHICAL_ZONE_EUROPE");
                } else if(foundCodes.includes('pol')) {
                    this.inputsTypeValuePairs[geographicalZoneId] = this.mapKeyToId("mapAttributeValueToOptionId", "GEOGRAPHICAL_ZONE_POLAND");
                } else {
                    this.inputsTypeValuePairs[geographicalZoneId] = '';
                } 
            break;

            case this.savedMappedIdsForLaterUse.travelSportList:
                const updatedSportDisciplinePayload: any[] = []; 

                if(!!value && isJsonString(value)) {
                    const parsedValue: any = JSON.parse(value);
                    
                    if(parsedValue && Array.isArray(parsedValue)) {
                        let highPerformanceCoverageCounter: number = 0;
                        let skiingOrSnowboardingCoverageCounter: number = 0;

                        parsedValue.forEach((sportDiscipline: any, index: number) => {
                            const newValue: any = {
                                'sportName': '',
                                'sportValues': [],
                            };

                            newValue.sportName = sportDiscipline.name;
                            
                            if(this.props.sportInsuranceCoverageStore?.dataSet && this.props.sportInsuranceCoverageStore?.dataSet.totalCount > 0) {
                                const filteredSportInsuranceCoverage: any = this.props.sportInsuranceCoverageStore?.dataSet.items.filter((x: any) => x.sportDisciplineId === sportDiscipline.id);
    
                                if(filteredSportInsuranceCoverage && Array.isArray(filteredSportInsuranceCoverage) && filteredSportInsuranceCoverage.length > 0) {
                                    filteredSportInsuranceCoverage.forEach((sportInsurance: any) => {
                                        if(sportInsurance.coverageType === 'HighPerformance') {
                                            highPerformanceCoverageCounter++;
                                        }
                                        if(sportInsurance.coverageType === 'SkiingOrSnowboarding') {
                                            skiingOrSnowboardingCoverageCounter++;
                                        }

                                        newValue.sportValues.push({
                                            'insurer': sportInsurance.insurer ? sportInsurance.insurer.name : '',
                                            'value': sportInsurance.coverageType,
                                        });
                                    });
                                }
                            }

                            let baseValueToSpread: any = {...this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "travelAdditionalOptions")]};

                            if(highPerformanceCoverageCounter === 3 && skiingOrSnowboardingCoverageCounter === 2) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: true
                                };
                            } else if(highPerformanceCoverageCounter === 3) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: false,
                                };
                            } else if(skiingOrSnowboardingCoverageCounter === 2) {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: true,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: false,
                                };
                            } else {
                                baseValueToSpread = {...baseValueToSpread,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "SKI_SNOW")]: false,
                                    [this.mapKeyToId("mapAttributeValueToOptionId", "PRO_SPORT")]: false,
                                };
                            }

                            this.inputsTypeValuePairs[this.mapKeyToId("mapAttributeNameToId", "travelAdditionalOptions")] = baseValueToSpread;
                            
                            sportDiscipline['sportPayload'] = newValue;
                            updatedSportDisciplinePayload.push(sportDiscipline);
                        });

                        this.setDelayedInputNewValue[this.savedMappedIdsForLaterUse.travelSportList] = JSON.stringify(updatedSportDisciplinePayload);
                    }
                }
            break;
            case this.savedMappedIdsForLaterUse.autoProductionYear:
                if(!!this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate]) {
                    const productionYearDate: Date = new Date(`01-01-${value}`);
                    const firstRegistrationDate: Date = new Date(this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate]);
                    if(productionYearDate.getTime() > firstRegistrationDate.getTime()) {
                        this.catchError(L('The date of first registration cannot be earlier than the production year.'), 'other', false, MessageBarType.error, true);
                    } else {
                        this.catchError('', 'other', true, MessageBarType.error, false);
                    }
                } else if(this.state.blockNextStepButton) {
                    this.catchError('', 'other', true, MessageBarType.error, false);
                }
            break;
            case this.savedMappedIdsForLaterUse.autoFirstRegistrationDate:
                if(!!value) {
                    const productionYearDate: Date = new Date(`01-01-${this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear]}`);
                    const firstRegistrationDate: Date = new Date(value);
                    if(productionYearDate.getTime() > firstRegistrationDate.getTime()) {
                        this.catchError(L('The date of first registration cannot be earlier than the production year.'), 'other', false, MessageBarType.error, true);
                    } else {
                        this.catchError('', 'other', true, MessageBarType.error, false);
                    }
                } else if(this.state.blockNextStepButton) {
                    this.catchError('', 'other', true, MessageBarType.error, false);
                }
            break;
            case this.savedMappedIdsForLaterUse.autoVin:
                if(!!value && value.length === 17 && value !== this.eurotaxInfoexpertFormData.vin) {
                    this.eurotaxInfoexpertFormData.vin = value;
                    this.getVehicleConfigByVin(value);
                }
            break;
        }

        if(caseFoundFlag === false) {
        //     if(id === this.mapAttributeNameToId('productionYear')) {
        //         this.customInputsData['productionYear'] = value;
        //         delete this.customInputsData['vehicleBrand'];
        //         delete this.customInputsData['vehicleModel'];
        //     }
    
        //     if(id === this.mapAttributeNameToId('vehicleType') && this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.totalCount > 0) {
        //         let attrData = getAttributeNameAndValue(this.product, this.props.productAttributeStore?.dataSet.items, id, value, this.gnLanguage, this.getMapNameByProduct(this.product));
        //         this.customInputsData['vehicleType'] = attrData.value;
        //     }
        }
    }

    private toggleMessageBar(which: string, type: MessageBarType, text: string, hide: boolean) {
        if(which === "other") {
            this.setState((prevState) => ({ ...prevState, message: { text: text, type: type ? type : MessageBarType.error } }));
        } else {
            this.summaryMessageBoxData[which].text = text;
            this.summaryMessageBoxData[which].type = type;
            this.summaryMessageBoxData[which].hide = hide;
        }
    }

    private catchError(error: any, callType: string, hide?: boolean, type?: MessageBarType, blockNextStepButton?: boolean) {
        let text = catchErrorMessage(error);

        if(callType === "other") {
            this.setState((prevState) => ({ ...prevState, message: (hide === true ? undefined : { text: text, type: !!type ? type : MessageBarType.error }),
                                            blockNextStepButton: typeof blockNextStepButton === 'boolean' ? blockNextStepButton : prevState.blockNextStepButton }));
        } else {
            this.summaryMessageBoxData[callType].text = text;
            this.summaryMessageBoxData[callType].type = MessageBarType.error;
            this.summaryMessageBoxData[callType].hide = false;
        }
    }

    private cloneProductAttributeMappingsData(productAttributeMappingsToChange: any[], productAttributeMappingsToClone: any[]) {
        this.savedProductAttributeMappings = {
            productAttributeMappingsToChange: productAttributeMappingsToChange,
            productAttributeMappingsToWatch: productAttributeMappingsToClone,
        };
        
        productAttributeMappingsToChange.forEach((productAttributeMappingToChange: any) => {
            let tempUserFields: any[] = [];

            if(productAttributeMappingToChange.ProductAttribute && productAttributeMappingToChange.ProductAttribute.UserFields) {
                tempUserFields = productAttributeMappingToChange.ProductAttribute.UserFields;
            } else if(productAttributeMappingToChange && productAttributeMappingToChange.UserFields) {
                tempUserFields = productAttributeMappingToChange.UserFields;
            }

            tempUserFields.some((UserField: any) => {
                if(UserField.Key === 'key') {
                    let splitedValue: string[] = UserField.Value.split('.');
                    let attributeIdOfClonedMapping: string = mapAttributeKeyToId(productAttributeMappingsToClone, splitedValue[splitedValue.length - 1]);
                    let valueToCopy: string = this.inputsTypeValuePairs[attributeIdOfClonedMapping];

                    if(!!valueToCopy) {
                        this.inputsTypeValuePairs[productAttributeMappingToChange.Id] = getValueBasedOnClonedValue(valueToCopy, productAttributeMappingToChange, productAttributeMappingsToClone, attributeIdOfClonedMapping);
                        this.inputsIdUserFieldsPairs[productAttributeMappingToChange.Id] = tempUserFields;
                    }
                    return true;
                }
                return false;
            });
        });

        this.controlledForceUpdate('line 419');
    }

    private async fillFormWithSelectedClientData(productAttributeMappingsToChange: any[]) {
        this.toggleAsyncActionFlag(true);

        if(this.tempSelectedClient && this.tempSelectedClient.length > 0) {
            let client: ClientDto = defaultClient; 
            
            if(this.selectedClientData && parseInt(this.tempSelectedClient) !== parseInt(this.selectedClientData.id)) {
                await this.props.clientStore?.get({...defaultClient, id: this.tempSelectedClient}).then((response: ClientDto) => {
                    client = response;
                    this.selectedClientData = response;
                }).catch((error: any) => {
                    this.catchError(error, "other");
                });
            } else if (this.selectedClientData) {
                client = this.selectedClientData;
            }

            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'FirstName').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'FirstName');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.name;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Surname').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Surname');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.surname;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Name').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Name');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.countryId;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'CompanyName').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'CompanyName');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.company;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Nip').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Nip');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.nip;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Regon').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Regon');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.regon;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'City').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'City');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.city;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Street').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Street');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.streetAddress;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'HouseNumber').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'HouseNumber');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.streetAddress2;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Birthdate').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Birthdate');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.dateOfBirth;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'MobilePhone').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'MobilePhone');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.phone;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'PostCode').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'PostCode');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.zipPostalCode;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Pesel').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Pesel');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.pesel;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Country').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Country');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.country;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'County').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'County');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.county;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Email').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Email');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.user.emailAddress;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'AdditionalEmail').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'AdditionalEmail');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.emailAdditional;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'ClientType').length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'ClientType');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                
                let filteredProductAttributeMapping: any = filterBySome(productAttributeMappingsToChange, "Id", attrId);
                let newValue = client.clientType;
                for(let key in filteredProductAttributeMapping.ProductAttributeValues) {
                    if(filteredProductAttributeMapping.ProductAttributeValues.hasOwnProperty(key) && 
                        filteredProductAttributeMapping.ProductAttributeValues[key].Name === client.clientType)
                    {
                        newValue = filteredProductAttributeMapping.ProductAttributeValues[key].Id;
                    }
                }
                
                this.inputsTypeValuePairs[attrId] = newValue;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }
            if(mapAttributeKeyToId(productAttributeMappingsToChange, 'Nationality').length > 0 && !!client.nationality && client.nationality.length > 0) {
                const attrId: string = mapAttributeKeyToId(productAttributeMappingsToChange, 'Nationality');
                this.inputsIdUserFieldsPairs[attrId] = getUserFieldsFromProductAttributeMapping(productAttributeMappingsToChange, attrId);
                this.inputsTypeValuePairs[attrId] = client.nationality;
                this.catchDataForCustomerInputs(attrId, this.inputsTypeValuePairs[attrId], this.inputsIdUserFieldsPairs[attrId]);
            }

            this.tempSelectedClient = "";
        }

        this.toggleAsyncActionFlag(false);
    }

    private async getSingleProduct(selectedProduct: string | number | undefined) {
        if(!selectedProduct || selectedProduct === this.selectedProduct) return;

        if(validateLocalStorageKeyAndTimestamp(`policyCalculationProduct${selectedProduct}`, hourInMs * 12, true)) {
            this.product = JSON.parse(sessionStorage.getItem(`policyCalculationProduct${selectedProduct}`)!).data;
            this.selectedProduct = selectedProduct;
            this.forceUpdate();
        } else {
            this.toggleAsyncActionFlag(true);
            this.changeLoadSpinnerLabel(L('Downloading product data.'));

            await this.props.productStore?.getProductWithMappingsForProductId(selectedProduct.toString()).then((productResult: any) => {
                this.product = productResult;

                saveInStorage(`policyCalculationProduct${selectedProduct}`, {data: this.product, timestamp: new Date().getTime()}, true);
                this.controlledForceUpdate('line 667');
            });

            this.selectedProduct = selectedProduct;
            this.toggleAsyncActionFlag(false, true);
        }
    }

    private async getVehicleConfigByVin(vin: string) {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        await vehicleConfigService.getByVin(vin).then((response: any) => {
            if(response && response.id > 0) {
                this.eurotaxInfoexpertFormData = response;
                this.customInputsData['vehicleTypeId'] = this.eurotaxInfoexpertFormData.type;
                this.customInputsData['vehicleBrandId'] = this.eurotaxInfoexpertFormData.brand;
                this.customInputsData['productionYear'] = this.eurotaxInfoexpertFormData.year;
                this.customInputsData['fuelType'] = this.eurotaxInfoexpertFormData.fuelType;
                this.customInputsData['vehicleModelId'] = this.eurotaxInfoexpertFormData.model;
                this.customInputsData['enginePower'] = parseInt(this.eurotaxInfoexpertFormData.enginePower);
                this.customInputsData['engineCapacity'] = parseInt(this.eurotaxInfoexpertFormData.engineCapacity);
                this.customInputsData['vehicleConfigurationEurotaxId'] = this.eurotaxInfoexpertFormData.eurotax;
                this.customInputsData['vehicleConfigurationInfoExpertId'] = this.eurotaxInfoexpertFormData.infoExpert;
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear] = !!this.eurotaxInfoexpertFormData.year ? parseInt(this.eurotaxInfoexpertFormData.year) : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoProductionYear];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoMileage] = !!this.eurotaxInfoexpertFormData.mileage ? this.eurotaxInfoexpertFormData.mileage : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoMileage];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate] = !!this.eurotaxInfoexpertFormData.firstRegistrationDate && this.eurotaxInfoexpertFormData.firstRegistrationDate.substring(0, 4) !== '0001' ? 
                                                                                                        this.eurotaxInfoexpertFormData.firstRegistrationDate : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoFirstRegistrationDate];
                this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoRegistrationNumber] = !!this.eurotaxInfoexpertFormData.registrationNumber ? this.eurotaxInfoexpertFormData.registrationNumber : this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoRegistrationNumber];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private async apiCall(step: number) {
        this.toggleAsyncActionFlag(true, true);

        switch(step) {
            case 1:
                // let isThisOriginProduct: boolean = (this.prevProductId && this.prevProductId?.length > 0 ? false : true);

                // if(this.product?.Id !== this.selectedProduct) {
                //     await this.props.productStore?.get({ id: this.selectedProduct } as ProductDto).then((productResult) => {
                //         this.product = productResult;
                //         this.clearDataOnProductChange();
    
                //         if(isThisOriginProduct && this.isEditMode) {
                //             this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...this.tempInputsTypeValuePairsFromEdit};
                //         }
    
                //         this.controlledForceUpdate('line 697');
                //     });
                // }

                if(!this.productAttributes || Object.keys(this.productAttributes).length <= 0) {
                    await this.props.productAttributeStore!.getAll(this.props.productAttributeStore!.defaultRequest);
                    this.productAttributes = this.props.productAttributeStore && this.props.productAttributeStore.dataSet && this.props.productAttributeStore.dataSet.items ? this.props.productAttributeStore.dataSet.items : {};
                    this.isDataLoaded = true;
                }

                if(!this.clientDataFilled && (!this.client || parseInt(this.client.id) <= 0)) {
                    this.client = await this.props.clientStore?.get({...defaultClient, id: this.selectedClient});
                    if(this.client && !!this.client.id) {
                        this.clientDataFilled = true;
                    }
                }

                if(this.autoCalculationOwner === AutoCalculationOwnerType.Insurer) {
                    this.cloneProductAttributeMappingsData(
                        this.savedProductAttributeMappings.productAttributeMappingsToChange,
                        this.savedProductAttributeMappings.productAttributeMappingsToWatch,
                    );
                }

                if(this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta")) {
                    this.changeLoadSpinnerLabel(L('Downloading user vehicles.'));
                    await this.props.vehicleStore?.getByClientId(this.selectedClient);
                    
                    this.changeLoadSpinnerLabel(L('Downloading user APK files.'));
                    await this.props.apkAttachedFilesStore?.getApkForManualPolicy(this.selectedClient, this.getProductTypeByProduct(this.product));

                    this.loadSpinnerCustomLabel = null;
                }

                this.clientDataFilled = true;

                break;
            case 2:
                if(this.manualPolicyCreateRequestSent === false) {
                    if(this.selectedProduct === this.mapKeyToId("mapProductNameToProductId", "ubezpieczenie-auta") && !!this.selectedClient && !!this.eurotaxInfoexpertFormData.eurotax) {
                        await vehicleConfigService.saveByVin({...this.eurotaxInfoexpertFormData, clientId: this.selectedClient}).then((response: any) => {}).catch((error: any) => {
                            this.catchError(error, "other");
                        });
                    }

                    const policyManualCreatePayload = {
                        ...buildRequestBody('policyManualCreate', this, this.getProductTypeByProduct(this.product), this.getMapNameByProduct(this.product)),
                        "policyNumber": this.inputsTypeValuePairs['policyNumber'],
                        "insurerId": this.inputsTypeValuePairs['insurerId'],
                        "policyTotal": this.inputsTypeValuePairs['policyTotal'],
                        "policyDate": this.inputsTypeValuePairs['policyDate'],
                        "startDate": this.inputsTypeValuePairs[this.savedMappedIdsForLaterUse.autoStartDate],
                        "endDate": this.inputsTypeValuePairs['endDate'],
                    };
                    
                    this.manualPolicyCreateRequestSent = true;

                    await insurancePolicyService.manualCreate(policyManualCreatePayload).then((response) => {
                        if(response && response.id && response.id > 0) {
                            this.summaryMessageBoxData['policyFinalization'].text = L('Success');
                            this.summaryMessageBoxData['policyFinalization'].type = MessageBarType.success;
                            this.summaryMessageBoxData['policyFinalization'].hide = false;
                            this.nextStep();
                        }
                    }).catch((error) => {
                        this.catchError(error, "other");
                    });
                }
                
                break;
            case 3:
                break;
        }
        
        this.toggleAsyncActionFlag(false);
        if(this.step < AppConfig.policyFormAllSteps) {
            this.step++;
        }

        this.controlledForceUpdate('line 841');
    }

    private nextStep = () => {
        // if(this.customerDataFilled && this.step === 1 && !this.dialogConfirmed && this.selectedCustomer) {
        //     this.showConfirmationDialog = true;
        //     this.controlledForceUpdate('line 850');
        // } else 

        if(this.step === 1) {
            this.adjustInputsChangedManually = {};

            for(let key in this.templateInputsForCalculationAdjust) {
                if(this.templateInputsForCalculationAdjust.hasOwnProperty(key)) {
                    delete this.inputsTypeValuePairs[key];
                }
            }
        }

        if(this.inputErrors === 0) {
            this.apiCall(this.step);
        }
    }

    private prevStep = () => {
        if(this.step === 2) {
            this.adjustInputsChangedManually = {};

            for(let key in this.templateInputsForCalculationAdjust) {
                if(this.templateInputsForCalculationAdjust.hasOwnProperty(key)) {
                    delete this.inputsTypeValuePairs[key];
                }
            }
        }

        if(this.step === 2 && this.blockNextStepButton === true) {
            this.blockNextStepButton = false;
        }

        if(this.step > 1) {
            this.step--;
        }

        if(this.step === 1) {
            this.dialogConfirmed = false;
        }

        if(this.step === 1 || this.step === 2) {
            this.inputsChangedManually = {};
        }

        this.controlledForceUpdate('line 878');
    }

    private dialogOnAction(confirm: boolean) {
        this.showConfirmationDialog = false;
        this.dialogConfirmed = true;
        this.clientDataFilled = !confirm;

        this.nextStep();
    }

    private toggleAsyncActionFlag(newState: boolean, forceUpdate?: boolean) {
        if(typeof newState === 'boolean') {
            this.setState((prevState) => ({ ...prevState, asyncActionInProgress: newState }));
            
            if(forceUpdate) {
                this.controlledForceUpdate('line 894');
            }
        }
    }

    private handleOnInputChange(id: string, value: any, userFields?: any, stepNumber?: number) {
        if(stepNumber === 1) {
            this.catchDataForCustomerInputs(id, value, userFields);
            this.catchDataForCustomInputs(id, value);
            this.inputsTypeValuePairs[id] = value; 
            this.inputsIdUserFieldsPairs[id] = userFields; 
        } else if(stepNumber === 2) {
            this.catchDataForCustomInputs(id, value);
            this.inputsTypeValuePairs[id] = value; 
            this.inputsIdUserFieldsPairs[id] = userFields;

            if(this.setDelayedInputNewValue && this.setDelayedInputNewValue[id]) {
                this.inputsTypeValuePairs[id] = this.setDelayedInputNewValue[id];
                this.setDelayedInputNewValue = {};
            }
            this.inputValuePairsStringified = JSON.stringify(this.inputsTypeValuePairs); 
        }
        this.controlledForceUpdate('line 1457');
    }

    handleChildTabChange = (selectedKey: string) => {
        this.tabsSelectedKey = selectedKey;
        this.forceUpdate();
    }

    renderConfirm = () => {
        if(this.step === 1 || this.step === 2) {
            let  buttonText: string = '';
            if(this.step === 1) {
                buttonText = L('Next step');
            } else if(this.step === 2) {
                buttonText = L('Save a policy');
            }

            let buttonIcon: string = '';
            if(this.step === 1) {
                buttonIcon = 'ChromeBackMirrored';
            } else if(this.step === 2) {
                buttonIcon = 'Accept';
            }

            return <div className={classNames.confrimButtonWrapper}>
                <PrimaryButton theme={myTheme} onClick={this.nextStep} text={buttonText} iconProps={{ iconName: buttonIcon }} 
                    disabled={!this.selectedProduct || this.state.asyncActionInProgress || this.inputErrors > 0 || this.blockNextStepButton || this.state.blockNextStepButton} />
                
                {this.state.asyncActionInProgress ? <Spinner label={typeof this.loadSpinnerCustomLabel === 'string' ? this.loadSpinnerCustomLabel : L('Please wait...')}
                    className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> : ''}
            </div>;
        } else {
            return <></>;
        }
    };
    
    renderBack = () => {
        let buttons = <DefaultButton text={L('Go back to list')} iconProps={{ iconName: 'ChevronLeft' }} onClick={this._onBack} allowDisabledFocus />;
        
        if(this.step > 1) {
            buttons = <>
                <DefaultButton text={L('Go back to list')} iconProps={{ iconName: 'ChevronLeft' }} onClick={this._onBack} allowDisabledFocus />
                { this.step === 2 &&
                    <DefaultButton text={L('Previous step')} iconProps={{ iconName: 'Back' }} onClick={this.prevStep} allowDisabledFocus disabled={this.state.asyncActionInProgress} />
                }
                {this.step === 2 && this.childTabSwitch && parseInt(this.tabsSelectedKey) !== this.tabs.length - 1 &&
                    <DefaultButton text={L('Next step')} iconProps={{iconName: 'ChromeBackMirrored'}} onClick={this.childTabSwitch} disabled={this.tabsSelectedKey === "2"} />
                }
            </>;
        }

        return buttons;
    };

    renderContent() {
        return <>
            <Dialog
                hidden={!this.showConfirmationDialog}
                onDismiss={() => this.dialogOnAction(false)}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L('Confirmation required'),
                    subText: L('Do you want to overwrite customer details?'),
                }}
                modalProps={{
                    isBlocking: true
                }}
            >
                <DialogFooter>
                    <PrimaryButton onClick={() => this.dialogOnAction(true)} text={L('Yes')} />
                    <DefaultButton onClick={() => this.dialogOnAction(false)} text={L('No')} />
                </DialogFooter>
            </Dialog>

            <h2 className={classNames.pageTitle}>{L("Client")}</h2>
            <PolicyFormStepsLayer step={this.step} hostId="calculation-steps-layer-host" />

            <LayerHost id="calculation-steps-layer-host" className={classNames.layerHost}>    
                {this.step === 1 &&
                    <Pivot className={classNames.toolbar} theme={myTheme}>
                        <PivotItem key={this.step}>
                            {this.step === 1 ? 
                                <PolicyFormStep1 productStore={this.props.productStore} allProducts={this.allProducts} clientStore={this.props.clientStore}
                                    selectedProduct={this.selectedProduct} selectedClient={this.selectedClient} tempSelectedClient={this.tempSelectedClient} 
                                    asyncActionInProgress={this.state.asyncActionInProgress} blockNextStepButton={this.blockNextStepButton}
                                    onProductSelect={(productId: string | number | undefined) => {
                                        this.getSingleProduct(productId); this.controlledForceUpdate('line 970');
                                    }}
                                    mapKeyToId={(mapType: string, key: string) => this.mapKeyToId(mapType, key)} selectedClientData={this.selectedClientData}
                                    allUserFields={this.allUserFields} countryStore={this.props.countryStore} productAttributes={this.productAttributes}
                                    onMassInputChange={(inputFields: any, userFields: any) => { this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...inputFields}; this.inputsIdUserFieldsPairs = {...this.inputsIdUserFieldsPairs, ...userFields}; this.loopInputsTypeValuePairs(); }} 
                                    onFillFormWithSelectedClientData={(productAttributeMappingsToChange: any[]) => this.fillFormWithSelectedClientData(productAttributeMappingsToChange)}
                                    onInputChange={(id: string, value: any, userFields?: any) => this.handleOnInputChange(id, value, userFields, this.step)}
                                    product={this.product} isDataLoaded={this.isDataLoaded} inputErrors={this.inputErrors}
                                    inputsTypeValuePairs={this.inputsTypeValuePairs} gnLanguage={this.gnLanguage}
                                    inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs} customerTypeValuePairs={this.clientTypeValuePairs} setInputErrors={(errorsCount: number) => { 
                                        this.inputErrors = errorsCount; if(errorsCount > 0) { this.catchError(L("Correct errors in the form and/or fill in the required fields."), "other", false, MessageBarType.warning); } else if(this.blockNextStepButton === false) { this.catchError("", "other", true); } this.controlledForceUpdate(); }} 
                                    setBlockNextStepButton={(value: boolean, message?: string) => {
                                        this.blockNextStepButton = value; if(value === true) { this.catchError(message, "other", false, MessageBarType.warning); } else if(this.inputErrors === 0) { this.catchError("", "other", true); } this.controlledForceUpdate(); }}
                                    onCustomerSelect={(customer: any, justId: boolean = false) => {
                                        if(!!justId && justId === true) {
                                            this.selectedClient = typeof customer === 'number' ? customer.toString() : customer;
                                        } else {
                                            this.selectedClient = customer && typeof customer !== 'undefined' ? typeof customer.id === 'number' ? customer.id.toString() : customer.id : ""; this.controlledForceUpdate();
                                        }
                                    }}
                                    onTempCustomerSelect={(tempCustomer: any, justId: boolean = false) => {
                                        if(!!justId && justId === true) {
                                            this.tempSelectedClient = typeof tempCustomer === 'number' ? tempCustomer.toString() : tempCustomer;
                                        } else {
                                            this.tempSelectedClient = tempCustomer && typeof tempCustomer !== 'undefined' ? typeof tempCustomer.id === 'number' ? tempCustomer.id.toString() : tempCustomer.id : ""; this.controlledForceUpdate();
                                        }
                                    }} onCloneProductAttributeMappingsData={(productAttributeMappingsToChange: any[], productAttributeMappingsToClone: any[]) => this.cloneProductAttributeMappingsData(productAttributeMappingsToChange, productAttributeMappingsToClone)}
                                    autoCalculationOwner={this.autoCalculationOwner} onAutoCalculationOwnerChange={(value: string) => { this.autoCalculationOwner = value; this.controlledForceUpdate(); } }
                                    toggleAsyncActionInProgress={(bool: boolean) => { this.toggleAsyncActionFlag(bool); }}
                                /> : <></>}
                        </PivotItem>
                    </Pivot>
                }

                {this.step === 2 &&
                    <PolicyFormStep2 isDataLoaded={this.isDataLoaded} customInputsData={this.customInputsData}
                        product={this.product} inputsTypeValuePairs={this.inputsTypeValuePairs} gnLanguage={this.gnLanguage} countryStore={this.props.countryStore}
                        sportDisciplineStore={this.props.sportDisciplineStore} insurerStore={this.props.insurerStore}
                        onInputChange={(id: string, value: any, userFields?: any) => this.handleOnInputChange(id, value, userFields, this.step)} apkAttachedFilesStore={this.props.apkAttachedFilesStore}
                        mapKeyToId={(mapType: string, key: string) => this.mapKeyToId(mapType, key)} inputValuePairsStringified={this.inputValuePairsStringified}
                        onMassInputChange={(inputFields: any, userFields: any) => { this.inputsTypeValuePairs = {...this.inputsTypeValuePairs, ...inputFields}; this.inputValuePairsStringified = JSON.stringify(this.inputsTypeValuePairs); this.inputsIdUserFieldsPairs = {...this.inputsIdUserFieldsPairs, ...userFields}; this.loopInputsTypeValuePairs(); }}
                        toggleAsyncActionFlag={(newState: boolean, forceUpdate: boolean) => this.toggleAsyncActionFlag(newState, forceUpdate) } inputsIdUserFieldsPairs={this.inputsIdUserFieldsPairs}
                        saveInputsForCalculationAdjust={(inputs: any) => { this.saveInputsForCalculationAdjust(inputs); }}
                        isFastCalculation={this.isFastCalculation} setIsFastCalculation={(value: boolean) => { this.isFastCalculation = value; this.controlledForceUpdate('line 958'); }}
                        setInputErrors={(errorsCount: number) => {
                            this.inputErrors = errorsCount; 
                            if(errorsCount > 0) { 
                                this.catchError(L(""), "other", false); // Correct errors in the form and/or fill in the required fields.
                            } else {
                                this.catchError("", "other", true);
                            }
                            this.controlledForceUpdate('line 960'); 
                        }}
                        inputErrors={this.inputErrors} allUserFields={this.allUserFields} savedMappedIdsForLaterUse={this.savedMappedIdsForLaterUse}
                        changeAdjustInputsChangedManually={(id: string, value: any) => { this.adjustInputsChangedManually[id] = value; this.controlledForceUpdate(); }}
                        inputsChangedManually={this.inputsChangedManually} asyncActionInProgress={this.state.asyncActionInProgress}
                        isEditMode={this.isEditMode} vehicleStore={this.props.vehicleStore} tempSelectedVehicle={this.tempSelectedVehicle}
                        onTempVehicleSelect={(tempVehicle: any, justId: boolean = false) => {
                            if(!!justId && justId === true) {
                                this.tempSelectedVehicle = typeof tempVehicle === 'number' ? tempVehicle.toString() : tempVehicle;
                            } else {
                                this.tempSelectedVehicle = tempVehicle && typeof tempVehicle !== 'undefined' ? typeof tempVehicle.id === 'number' ? tempVehicle.id.toString() : tempVehicle.id : "";
                            }
                            this.controlledForceUpdate();
                        }}
                        tempSelectedApk={this.tempSelectedApk} history={this.props.history}
                        onTempApkSelect={(tempApk: any) => {
                            this.tempSelectedApk = tempApk && typeof tempApk !== 'undefined' ? tempApk : undefined; 
                            this.controlledForceUpdate();
                        }}
                        formTabSwitch={this.formChildTabSwitch} insurers={this.insurers}
                        onTabChange={this.handleChildTabChange} productAttributes={this.productAttributes}
                        setEurotaxInfoexpertFormData={(id: string, value: string) => { this.eurotaxInfoexpertFormData[id] = value; this.forceUpdate(); }}
                        savedTemplateInputsForTable={this.savedTemplateInputsForTable}
                        setSavedTemplateInputsForTable={(value: any) => { this.savedTemplateInputsForTable = value; this.forceUpdate(); }}
                    />
                }

                {this.step === 3 &&
                    <PolicyFormStep3 gnLanguage={this.gnLanguage} asyncActionInProgress={this.state.asyncActionInProgress}
                        isEditMode={this.isEditMode} summaryMessageBoxData={this.summaryMessageBoxData}
                        onMessageBarDismiss={(type: string) => { this.summaryMessageBoxData[type].hide = true; this.controlledForceUpdate(); } }
                    />
                }
            </LayerHost>
        </>;
    }
}