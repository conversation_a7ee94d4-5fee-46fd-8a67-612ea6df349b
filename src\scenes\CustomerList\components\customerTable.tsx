import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { isGranted, L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { CustomerPanel } from './customerPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { dateFormat, formatPhoneNumber } from "../../../utils/utils";
import { RouterPath } from "../../../components/Router/router.config";
import { ClientDto } from "../../../services/client/dto/clientDto";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import { myTheme } from "../../../styles/theme";
import clientService from "../../../services/client/clientService";
import { Dialog, DialogType } from "@fluentui/react";

export class CustomerTable extends FluentTableBase<ClientDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  
  disableGetAllOnMount = true;

  getItemDisplayNameOf(item: ClientDto): string {
    if(!!item.company) {
      return item.company;
    } else if(!!item.user) {
      return `${item.user.name} ${item.user.surname}`; 
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return CustomerTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Client name'),
        fieldName: 'fullName',
        minWidth: 350,
        maxWidth: 350,
      },
      // {
      //   name: L('GDPR consents'),
      //   fieldName: 'agreementsStatus',
      //   minWidth: 80,
      //   maxWidth: 100,
      //   onRender: (item: ClientDto): any => {
      //     return <Icon className={classNames.statusIcon}
      //               style={!!item.agreementsStatus && item.agreementsStatus === ClientAgreementsStatusEnum.Signed ? {color: "green"} :
      //                 (!!item.agreementsStatus && item.agreementsStatus === ClientAgreementsStatusEnum.Sended ? {color: "orange"} : {color: "red"})}
      //               iconName={!!item.agreementsStatus && item.agreementsStatus === ClientAgreementsStatusEnum.Signed ?
      //                 "SkypeCheck" : (!!item.agreementsStatus && item.agreementsStatus === ClientAgreementsStatusEnum.Sended ? "HourGlass" : "StatusCircleErrorX")}
      //             />
      //   }
      // },
      {
        name: L('Pesel'),
        fieldName: 'pesel',
        minWidth: 80,
        maxWidth: 100,
        onRender: (item: ClientDto): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.pesel;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Regon'),
        fieldName: 'regon',
        minWidth: 80,
        maxWidth: 100,
      },
      {
        name: L('NIP'),
        fieldName: 'nip',
        minWidth: 80,
        maxWidth: 100,
      },
      {
        name: L('E-mail'),
        fieldName: 'user.emailAddress',
        onRender: (item: any): any => {
          return (item.user && !!item.user.emailAddress) ? item.user.emailAddress : '';
        }
      },
      {
        name: L('Additional e-mail'),
        fieldName: 'emailAdditional',
      },
      {
        name: L('Phone'),
        fieldName: 'phone',
        onRender: (item: any): any => {
          return (item.phone && !!item.phone) ? formatPhoneNumber(item.phone) : '';
        }
      },
      {
        name: L('Note'),
        fieldName: 'note',
        onRender: (item: any): any => {
          return <span title={item.note}>{item.note}</span>
        }
      },
      {
        name: L('Registered'),
        fieldName: 'creationTime',
        onRender: (item: any): any => {
          return !!item.creationTime ? dateFormat(item.creationTime, undefined, true) : '-';
        }
      },
      {
        name: L('Agent'),
        fieldName: 'creatorUser',
        onRender: (item: ClientDto): any => {
          return item.creatorUser ? item.creatorUser.fullName : '-';
        }
      },
      {
        name: L('Last modification'),
        fieldName: 'lastModificationTime',
        onRender: (item: any): any => {
          return !!item.lastModificationTime ? dateFormat(item.lastModificationTime, undefined, true) : '-';
        }
      },
      {
        name: L('Editor'),
        fieldName: 'lastModifierUser',
        onRender: (item: ClientDto): any => {
          return item.lastModifierUser ? item.lastModifierUser.fullName : '-';
        }
      },
      {
        name: L('Customer type'),
        fieldName: 'clientType',
        onRender: (item: ClientDto): any => {
          return L(item.clientType);
        }
      },
    ];
  }

  onItemInvoked = (item: any): void => {
    this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=customer&entityId=${item.id}`);
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: true,
    };
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      customActionsProps: [
        isGranted('Client.Create.A') || isGranted('Client.Create.S') || isGranted('Client.Create.O') ? {
          displayFor: 'none',
          buttonText: L("New"),
          buttonIcon: 'none',
          buttonColor: myTheme.palette.white,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.themePrimary,
          buttonBorder: `1px solid ${myTheme.palette.themePrimary}`,
        } : undefined,
        isGranted('PolicyCalculation.Create.A') ? {
          displayFor: 'single',
          buttonText: L("Create calculation"),
          buttonIcon: 'none',
          buttonColor: myTheme.palette.white,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.themePrimary,
          buttonBorder: `1px solid ${myTheme.palette.themePrimary}`,
        } : undefined,
        isGranted('Client.Update.A') || isGranted('Client.Update.S') || isGranted('Client.Update.O') ? {
          displayFor: 'single',
          buttonText: L("Details"),
          buttonIcon: 'none',
        } : undefined,
        isGranted('PolicyCalculation.Create.A') ? {
          displayFor: 'single',
          buttonText: L("Create a policy manually"),
          buttonIcon: "none",
        } : undefined,
        isGranted('Apk.Create.A') ? {
          displayFor: 'single',
          buttonText: L("Create APK"),
          buttonIcon: "none",
        } : undefined,
        isGranted('PolicyCalculation.Create.A') ? {
          displayFor: 'single',
          buttonText: L("Create OC termination"),
          buttonIcon: "none",
        } : undefined,
        isGranted('Client.Duplicate.A') || isGranted('Client.Duplicate.S') || isGranted('Client.Duplicate.O') ? {
          displayFor: 'single',
          buttonText: L("Duplicate"),
          buttonIcon: "none",
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.black,
        } : undefined,
        isGranted('Client.Delete.A') || isGranted('Client.Delete.S') || isGranted('Client.Delete.O') ? {
          displayFor: 'single',
          buttonText: L("Delete"),
          buttonIcon: "none",
          buttonColor: myTheme.palette.red,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.white,
          buttonBorder: `1px solid ${myTheme.palette.red}`,
        } : undefined,
      ],
      customActions: [
        isGranted('Client.Create.A') || isGranted('Client.Create.S') || isGranted('Client.Create.O') ?
          (item: ClientDto) => {
            this.createOrUpdateModalOpen({ id: '' } as ClientDto);
          } : undefined,
        isGranted('PolicyCalculation.Create.A') ?
          (item: ClientDto) => {
            this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=customer&entityId=${item.id}`);
          } : undefined,
        isGranted('Client.Update.A') || isGranted('Client.Update.S') || isGranted('Client.Update.O') ?
          (item: ClientDto) => {
            this.createOrUpdateModalOpen(item);
          } : undefined,
        isGranted('PolicyCalculation.Create.A') ?
          (item: ClientDto) => {
            this.props.history.push(`/${RouterPath.PolicyForm}/?entityType=customer&entityId=${item.id}`);
          } : undefined,
        isGranted('Apk.Create.A') ?
          (item: ClientDto) => {
            this.props.history.push(`/${RouterPath.ApkForm}/?entityType=customer&entityId=${item.id}`);
          } : undefined,
        isGranted('PolicyCalculation.Create.A') ?
          (item: ClientDto & {fullName: string}) => {
            this.props.history.push(`/${RouterPath.OcTermination}/?entityType=customer&entityId=${item.id}||${item.fullName}`);
          } : undefined,
        isGranted('Client.Duplicate.A') || isGranted('Client.Duplicate.S') || isGranted('Client.Duplicate.O') ? 
          async (item: ClientDto) => {
            let copyResult = await clientService.copyClient(parseInt(item.id));

            if(copyResult && copyResult.hasOwnProperty('id')) {
              this.reloadItems();
              this.createOrUpdateModalOpen(copyResult);
            } else {
              this.togglePopUpDialog("Error", "Error occured during attempt of client duplicate.");
            }
          } : undefined,
        isGranted('Client.Delete.A') || isGranted('Client.Delete.S') || isGranted('Client.Delete.O') ? 
          (item: ClientDto) => {
            this.showDialog([item]);
          } : undefined,
      ]
    }
  }

  getTitle(): string {
    return L('Customer list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>
      
      <CustomerPanel
        {...props}
      />
    </>;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}